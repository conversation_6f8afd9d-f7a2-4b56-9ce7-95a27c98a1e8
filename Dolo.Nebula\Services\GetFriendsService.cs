using Dolo.Core.Http;
using Dolo.Nebula.Entities;
using Dolo.Nebula.Enum;
using Newtonsoft.Json;
namespace Dolo.Nebula.Services;

internal static class GetFriendsService
{
    public static string GetQuery() => "query getRelationships($profileId: String!){ relationships(profileId: $profileId) { nodes { profileId } } requestsIn(profileId: $profileId) { nodes { profileId } } requestsOut(profileId: $profileId) { nodes { profileId } } blocked(profileId: $profileId) { nodes { profileId } } blockedBy(profileId: $profileId) { nodes { profileId } } }";

    public static async Task<RelationshipData?> GetFriendsAsync(NebulaClient client, string? profileid = default)
    {
        if (string.IsNullOrEmpty(client.Config.Auth))
            throw new NullReferenceException("authkey is required on this method");

        var graph = new GraphQuery
        {
            Query = GetQuery(),
            Variables = JsonConvert.SerializeObject(new Dictionary<string, object?> { { "profileId", string.IsNullOrEmpty(profileid) ? client.Config.GetAccessToken()?.ProfileId : profileid } }),
            Operation = null
        };

        var http = await Http.TrySendAsync<object>(a => {
            a.Url = $"{client.Services?.GetEdgeRelationships()}/graphql";
            a.Method = HttpMethod.Post;
            a.ContentType = HttpContentType.ApplicationJson;
            a.AuthToken = client.Config.Auth;
            a.Content = new StringContent(JsonConvert.SerializeObject(graph));
        });

        RelationshipData? relationship = new();

        return await TryParseRelationships(client, http.Body?.ToString(), relationship) ? relationship : default;
    }

    private static async Task<bool> TryParseRelationships(NebulaClient nebula, string? json, RelationshipData relationshipData)
    {
        if (string.IsNullOrEmpty(json))
            return false;

        var data = JsonConvert.DeserializeObject<dynamic?>(json);

        if (data is null)
            return false;

        var profiles = new List<string>();

        foreach (var node in data.data.relationships.nodes)
            relationshipData.Friends.Add(new()
            {
                ProfileId = node.profileId,
                State = RelationshipState.Approved
            });

        foreach (var node in data.data.requestsIn.nodes)
            relationshipData.Requests.Add(new()
            {
                ProfileId = node.profileId,
                State = RelationshipState.WaitingForOwnApproval
            });


        foreach (var node in data.data.requestsOut.nodes)
            relationshipData.RequestsSent.Add(new()
            {
                ProfileId = node.profileId,
                State = RelationshipState.WaitingForOtherApproval
            });

        foreach (var node in data.data.blocked.nodes)
            relationshipData.Blocked.Add(new()
            {
                ProfileId = node.profileId,
                State = RelationshipState.BlockedBySelf
            });

        foreach (var node in data.data.blockedBy.nodes)
            relationshipData.BlockedByOther.Add(new()
            {
                ProfileId = node.profileId,
                State = RelationshipState.BlockedByOther
            });

        profiles.AddRange(relationshipData.Friends.Select(a => a.ProfileId));
        profiles.AddRange(relationshipData.Requests.Select(a => a.ProfileId));
        profiles.AddRange(relationshipData.RequestsSent.Select(a => a.ProfileId));
        profiles.AddRange(relationshipData.Blocked.Select(a => a.ProfileId));
        profiles.AddRange(relationshipData.BlockedByOther.Select(a => a.ProfileId));

        var profilesArray = profiles.ToArray();
        var pfr = (await nebula.GetProfilesAsync(profilesArray))?.GetProfiles();

        if (pfr is null)
            return false;

        foreach (var profile in pfr)
        {
            if (relationshipData.Friends.Any(a => a.ProfileId == profile.Id))
            {
                var pf = relationshipData.Friends.Single(a => a.ProfileId == profile.Id);
                pf.Avatar = profile.Avatar;
                pf.Culture = profile.Culture;
                pf.Membership = profile.Membership;
                pf.Name = profile.Name;
                pf.Level = profile.Level;
                pf.ProfileId = profile.Id;
            }
            if (relationshipData.Requests.Any(a => a.ProfileId == profile.Id))
            {
                var pf = relationshipData.Requests.Single(a => a.ProfileId == profile.Id);
                pf.Avatar = profile.Avatar;
                pf.Culture = profile.Culture;
                pf.Membership = profile.Membership;
                pf.Name = profile.Name;
                pf.Level = profile.Level;
                pf.ProfileId = profile.Id;
            }
            if (relationshipData.RequestsSent.Any(a => a.ProfileId == profile.Id))
            {
                var pf = relationshipData.RequestsSent.Single(a => a.ProfileId == profile.Id);
                pf.Avatar = profile.Avatar;
                pf.Culture = profile.Culture;
                pf.Membership = profile.Membership;
                pf.Name = profile.Name;
                pf.Level = profile.Level;
                pf.ProfileId = profile.Id;
            }
            if (relationshipData.Blocked.Any(a => a.ProfileId == profile.Id))
            {
                var pf = relationshipData.Blocked.Single(a => a.ProfileId == profile.Id);
                pf.Avatar = profile.Avatar;
                pf.Culture = profile.Culture;
                pf.Membership = profile.Membership;
                pf.Name = profile.Name;
                pf.Level = profile.Level;
                pf.ProfileId = profile.Id;
            }
            if (relationshipData.BlockedByOther.Any(a => a.ProfileId == profile.Id))
            {
                var pf = relationshipData.BlockedByOther.Single(a => a.ProfileId == profile.Id);
                pf.Avatar = profile.Avatar;
                pf.Culture = profile.Culture;
                pf.Membership = profile.Membership;
                pf.Name = profile.Name;
                pf.Level = profile.Level;
                pf.ProfileId = profile.Id;
            }
        }

        return true;
    }
}
