using Dolo.Core.Extension;
using Dolo.Planet.Enums;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
namespace Dolo.Planet;

public class MspConfig
{
    /// <summary>
    ///     Username to login
    /// </summary>
    public string? Username { get; set; }
    /// <summary>
    ///     Password to login
    /// </summary>
    public string? Password { get; set; }
    /// <summary>
    ///     Option to process the session id's after each 5 minute.
    /// </summary>
    public bool IsSessionProcessing { get; internal set; }
    /// <summary>
    ///     Option to process fetch information.
    /// </summary>
    public bool IsFetchProcessing { get; internal set; }

    /// <summary>
    ///     Option to behaviour as a original msp user
    /// </summary>
    public bool IsOriginalBehaviour { get; internal set; }
    /// <summary>
    ///     Option to use json intend
    /// </summary>
    public bool IsJsonIntend { get; internal set; }
    /// <summary>
    ///     Option to debug bruteforce
    /// </summary>
    public bool IsBruteForceDebug { get; internal set; }
    /// <summary>
    ///     Option to use multiple task on bruteforce
    /// </summary>
    public bool IsBruteForceMultiTask { get; internal set; }
    /// <summary>
    ///     Option for shard to process session ids
    /// </summary>
    public bool IsShardClient { get; internal set; }

    /// <summary>
    ///   Represents the logger
    /// </summary>
    public ILoggerFactory? Logger { get; set; }

    /// <summary>
    ///     Represents the server to which the client is connected.
    /// </summary>
    public Server? Server { get; set; }
    
    /// <summary>
    /// Override the proxy on all methods with the given proxy.
    /// useful for residental proxy.
    /// </summary>
    public WebProxy? CrossProxy { get; set; }

    /// <summary>
    ///     Use this option to enable debugging
    /// </summary>
    public MspConfig UseLogger(Action<ILoggingBuilder> builder)
    {
        Logger = LoggerFactory.Create(builder);
        return this;
    }

    /// <summary>
    ///     Use this option to enable session processing
    /// </summary>
    public MspConfig UseSessionProcessing()
    {
        IsSessionProcessing = true;
        return this;
    }
    /// <summary>
    ///     Use this option to enable fetch processing
    /// </summary>
    public MspConfig UseFetchProcessing()
    {
        IsFetchProcessing = true;
        return this;
    }
    /// <summary>
    ///     Use this option to behave as an original MSP user
    /// </summary>
    public MspConfig UseOriginalBehaviour()
    {
        IsOriginalBehaviour = true;
        return this;
    }


    /// <summary>
    ///     Use this option to enable json intend
    /// </summary>
    public MspConfig UseJsonIntend() {
        IsJsonIntend = true;
        return this;
    }

    /// <summary>
    ///     Use this option to enable debugging on brute force
    /// </summary>
    public MspConfig UseBruteForceDebug()
    {
        IsBruteForceDebug = true;
        return this;
    }
    /// <summary>
    ///     Use this option to enable multiple tasks on brute force
    /// </summary>
    public MspConfig UseBruteForceMultiTask()
    {
        IsBruteForceMultiTask = true;
        return this;
    }

    /// <summary>
    ///     Set the username to login
    /// </summary>
    public MspConfig SetUsername(string? username)
    {
        Username = username;
        return this;
    }

    /// <summary>
    ///     Set the password to login
    /// </summary>
    public MspConfig SetPassword(string? password)
    {
        Password = password;
        return this;
    }

    /// <summary>
    ///     Set the server to connect
    /// </summary>
    public MspConfig SetServer(Server server)
    {
        Server = server;
        return this;
    }

    /// <summary>
    /// Use proxy across all methods
    /// </summary>
    /// <param name="proxy"></param>
    /// <returns></returns>
    public MspConfig UseCrossProxy(WebProxy proxy)
    {
        CrossProxy = proxy;
        return this;
    }
}