﻿namespace Dolo.Bot.Pixi.Hub;

public static class HubEmoji
{
    public static DiscordEmoji? Yes
        => Hub.Pluto?.Emojis[823974901173977119];
    public static DiscordEmoji? No
        => Hub.Pluto?.Emojis[823991971425157131];
    public static DiscordEmoji? Loading
        => Hub.Pluto?.Emojis.TryGetValue(1223723406151913472, out var emoji) == true ? emoji : null;
    public static DiscordEmoji? Pluto
        => Hub.Pluto?.Emojis.TryGetValue(959534436817453097, out var emoji) == true ? emoji : null;
    public static DiscordEmoji? Astro
        => Hub.Pluto?.Emojis.TryGetValue(960572938673852486, out var emoji) == true ? emoji : null;
    public static DiscordEmoji? Heart
        => Hub.Pluto?.Emojis[823974911189581873];
    public static DiscordEmoji? Vip
        => Hub.Skid?.Emojis[806010463644549201];
    public static DiscordEmoji? Judge
        => Hub.Skid?.Emojis[806010463086182482];
    public static DiscordEmoji? Fame
        => Hub.Skid?.Emojis[806006180707041291];
    public static DiscordEmoji? Jury
        => Hub.Skid?.Emojis[806010463287771146];
    public static DiscordEmoji? Celeb
        => Hub.Skid?.Emojis[806010463279644702];
    public static DiscordEmoji? MspHeart
        => Hub.MovieStar?.Emojis.TryGetValue(979352003895508992, out var emoji) == true ? emoji : null;
    public static DiscordEmoji? Nike
        => Hub.Pluto?.Emojis[823991971928080414];
    public static DiscordEmoji? Skull
        => Hub.Pluto?.Emojis[823975006887346186];
    public static DiscordEmoji? Gift
        => Hub.Pluto?.Emojis.TryGetValue(917856848705634355, out var emoji) == true ? emoji : null;
}