using Dolo.Core;
using Dolo.Core.Http;
using Dolo.Nebula.Entities;
namespace Dolo.Nebula.Services;

internal static class GetProfileSearchService
{
    public static string GetQuery() => "query search($region: String!, $startsWith: String!, $pageSize: Int, $currentPage: Int, $preferredGameId: String!) { findProfiles(region: $region, nameBeginsWith: $startsWith, pageSize: $pageSize, page: $currentPage) { totalCount nodes { id avatar(preferredGameId: $preferredGameId) { gameId face full } } } }";

    public static async Task<List<ProfileData>?> SearchProfileAsync(NebulaClient client, string search, int pageSize, int currentPage = 0)
    {
        if (string.IsNullOrEmpty(client.Config.Auth))
            throw new NullReferenceException("authkey is required on this method");

        var graph = new GraphQuery
        {
            Query = GetQuery(),
            Variables = string.Format("{{\"region\":\"{0}\",\"startsWith\":\"{1}\",\"pageSize\":{2},\"currentPage\":{3},\"preferredGameId\":\"{4}\"}}", client.Config.Server.GetValueOrDefault().GetNebulaCodeFromServer().ToUpper(), search, pageSize, currentPage + 1, client.Config.Game.GetValueOrDefault().GetNebulaGame()),
            Operation = null
        };

        var http = await Http.TrySendAsync(a => {
            a.Url = $"{client.Services?.GetEdgeRelationships()}/graphql";
            a.Method = HttpMethod.Post;
            a.ContentType = HttpContentType.ApplicationJson;
            a.AuthToken = client.Config.Auth;
            a.Content = new StringContent(graph.ToJson()!);
        });

        if (!http.IsSuccess)
            return default;

        var httpBody = http.Body?.ToString();
        if (httpBody is null || !httpBody.IsValidJson())
            return default;

        var data = httpBody.TryParse<dynamic>();

        if (data is null)
            return default;

        var profileList = new List<string>();
        foreach (var node in data.data.findProfiles.nodes)
            profileList.Add(((dynamic)node).id.ToString());


        var profiles = await client.GetProfilesAsync(profileList.ToArray());
        return profiles?.Data?.Profiles?.ToList();
    }
}
