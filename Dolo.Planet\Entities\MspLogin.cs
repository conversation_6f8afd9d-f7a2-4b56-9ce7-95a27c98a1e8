using Dolo.Planet.Enums;
namespace Dolo.Planet.Entities;

public sealed class MspLogin(bool isLoggedIn = false) : MspBaseHttp
{
    public LoginStatusCode StatusCode { get; internal set; }
    public new string? Status { get; internal set; }
    public string? WebServiceUrl { get; internal set; }
    public bool LoggedIn { get; internal set; } = isLoggedIn;
    public MspUser Actor { get; internal set; } = new();
    public Server Server { get; internal set; }
    public string? GetGenderName() => Enum.GetName(Actor.Gender);

    public MspLogin SetAccessToken(string? token)
    {
        if (Actor.Nebula != null)
            Actor.Nebula.AccessToken = token;
        return this;
    }

    public MspLogin SetRefreshToken(string? token)
    {
        if (Actor.Nebula != null)
            Actor.Nebula.RefreshToken = token;
        return this;
    }

    public MspLogin SetProfileId(string? id)
    {
        Actor.ProfileId = id;
        return this;
    }

    public MspLogin SetServer(Server server)
    {
        Server = server;
        return this;
    }

    public MspLogin SetStatus(string? status)
    {
        Status = status;
        return this;
    }

}