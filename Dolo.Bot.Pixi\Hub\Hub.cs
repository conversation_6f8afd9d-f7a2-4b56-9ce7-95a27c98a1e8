using Dolo.Database;
using Dolo.Nebula;
using Dolo.Planet;
using Dolo.Planet.Enums;
namespace Dolo.Bot.Pixi.Hub;

public static class Hub
{
    public static string BotInviteUrl
        => "https://ptb.discord.com/api/oauth2/authorize?client_id=729110700332417065&permissions=8&scope=bot%20applications.commands";
    public static   DiscordClient Discord { get; set; } = null!;
    public static DiscordGuild? Pluto { get; set; }
    public static DiscordGuild? Skid { get; set; }
    public static DiscordGuild? MovieStar { get; set; }
    public static MspClientShard? MspShard { get; set; }
    public static Dictionary<Server, NebulaClient> NebulaClients { get; set; } = new();
    public static bool IsReady { get; set; }
    public static bool IsInitializing { get; set; }
    public static bool IsSyncing { get; set; }

    #if DEBUG
    public static bool IsDebug
        => true;
    #else
    public static bool IsDebug => false;
    #endif

    /// <summary>
    ///     Checks if a cheater is a whitelist
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public static async Task<bool> IsCheaterWhitelistAsync(string? id)
    {
        var settings = await Mongo.PixiSettings.GetFirstAsync();
        return settings?.HasWhitelist(id) ?? false;
    }

    /// <summary>
    ///     Checks if a cheater is in the database
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public static async Task<bool> IsCheaterAsync(string? id)
    {
        var settings = await Mongo.PixiSettings.GetFirstAsync();
        return settings?.HasCheater(id) ?? false;
    }

    /// <summary>
    ///     Checks if shop advertise is enabled
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public static async Task<bool> IsShopAsync()
    {
        var settings = await Mongo.PixiSettings.GetFirstAsync();
        return settings?.IsShop ?? false;
    }

    /// <summary>
    ///     Checks if the member is banned from the server
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public static async Task<bool> IsMemberBannedAsync(ulong id)
    {
        var member = await Mongo.ServerMembers.GetOneAsync(x => x.Member.DiscordId == id);
        return member?.State.IsBanned ?? false;
    }

    /// <summary>
    ///     Checks if the user is blocked
    /// </summary>
    public static async Task<bool> IsUserBlockedAsync(ulong id)
    {
        var settings = await Mongo.PixiSettings.GetFirstAsync();
        return settings != null && settings.HasBlockUser(id);
    }

    /// <summary>
    ///     Get the embed color from the settings
    /// </summary>
    public static async Task<string> GetEmbedColorAsync()
    {
        var settings = await Mongo.PixiSettings.GetFirstAsync();
        return settings?.EmbedColor ?? "#FFD700";
    }

}