﻿using Dolo.Core;
using Dolo.Core.Discord;
using Dolo.Core.Extension;
using Dolo.Database;
using Dolo.Planet;
using Dolo.Planet.Enums;
using Dolo.Pluto.Shard.Bot.Pixi;
using DSharpPlus.SlashCommands.Attributes;
using Newtonsoft.Json;
using System.Text;
namespace Dolo.Bot.Pixi.Hub.Global;

public partial class Msp
{
    [Command("track")]
    [Description("get tracking infos of a user")]
    [SlashCooldown(2, 30, SlashCooldownBucketType.User)]
    public async Task TrackingAsync(SlashCommandContext ctx,
        [Description("player server")] Server server,
        [Description("player username")] string username,
        [Description("dump to JSON format")] bool json = false)
    {
        await ctx.LogAsync($"/msp track {username} {server} {json}");

        // check if the command can be executed
        if (!await ctx.IsOkayAsync(true)) return;

        // check if the shard is ready to use 
        var msp = await ctx.IsShardReadyAsync(server);
        if (msp is null) return;

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » {MspClientUtil.GetServerDiscordFlag(server)} » **Fetching `{username}` ..**");
        var uId = await msp.GetActorIdAsync(username);
        if (!uId.IsAvailable)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » {MspClientUtil.GetServerDiscordFlag(server)} » **`{username}` does not exist**");
            return;
        }

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » {MspClientUtil.GetServerDiscordFlag(server)} » **Searching for tracked user `{username}` ..**");

        // get the id of the user if the connection failed return
        var user = await Mongo.PixiTracking.GetOneAsync(a => a.Stats.Any(b => b.Actor.Id == uId.Id));
        if (user is null)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.Pluto} » {MspClientUtil.GetServerDiscordFlag(server)} » **We have no tracking information for `{username}`**");
            return;
        }

        await ctx.TryEditResponseAsync($"-# {HubEmoji.Loading} » {MspClientUtil.GetServerDiscordFlag(server)} » **Dumping tracking information for `{username}` ..**");

        var actor = await msp.GetActorAsync(user.Actor.Id);
        var builder = new DiscordWebhookBuilder();
        var stringBuilder = new StringBuilder();
        var rawBuilder = new StringBuilder();
        // order the stats 
        var comparer = user.Stats.Skip(1).Distinct(new TrackingComporer()).OrderByDescending(a => a.TrackedAt).ToList();

        var embed = new DiscordEmbedBuilder()
            .WithThumbnail(actor.Avatar.AvatarUrl)
            .WithTitle($"{user.Actor.Username} » <t:{user.Stats.FirstOrDefault()?.TrackedAt.ToTimestamp()}>")
            .WithDescription(new StringBuilder()
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [level](https://a) » {user.Actor.Level}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Fame](https://a) » {user.Actor.Fame.ToString("N0")}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [StarCoins](https://a) » {user.Actor.StarCoins.ToString("N0")}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Diamonds](https://a) » {user.Actor.Diamonds.ToString("N0")}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Friends](https://a) » {user.Actor.Friends.ToString("N0")}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [ProfileViews](https://a) » {user.Actor.ProfileViews.ToString("N0")}**").ToString())
            .WithColor(new(await Hub.GetEmbedColorAsync()));

        if (comparer.Count == 0)
            embed.WithImageUrl(actor.Avatar.BodyUrl);

        builder.AddEmbed(embed);
        foreach (var comp in comparer)
        {
            rawBuilder.AppendLine($"Tracked at {comp.TrackedAt}")
                .AppendLine($"» level » {comp.Actor.Level}")
                .AppendLine($"» Fame » {comp.Actor.Fame.ToString("N0")}")
                .AppendLine($"» StarCoins » {comp.Actor.StarCoins.ToString("N0")}")
                .AppendLine($"» Diamonds » {comp.Actor.Diamonds.ToString("N0")}")
                .AppendLine($"» Friends » {comp.Actor.Friends.ToString("N0")}")
                .AppendLine($"» ProfileViews » {comp.Actor.ProfileViews.ToString("N0")}");

            rawBuilder.AppendLine();

            stringBuilder.AppendLine($"**Tracked at <t:{comp.TrackedAt.ToTimestamp()}>**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [level](https://a) » {comp.Actor.Level}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Fame](https://a) » {comp.Actor.Fame.ToString("N0")}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [StarCoins](https://a) » {comp.Actor.StarCoins.ToString("N0")}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Diamonds](https://a) » {comp.Actor.Diamonds.ToString("N0")}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Friends](https://a) » {comp.Actor.Friends.ToString("N0")}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [ProfileViews](https://a) » {comp.Actor.ProfileViews.ToString("N0")}**");

            stringBuilder.AppendLine();
            if (stringBuilder.Length <= 2048)
                continue;

            builder.AddEmbed(new DiscordEmbedBuilder()
                .WithDescription(stringBuilder.ToString())
                .WithColor(new(await Hub.GetEmbedColorAsync())));
            stringBuilder.Clear();
        }
        foreach (var row in HubEmbed._buttons(ctx.Guild.Id))
        {
            builder.AddActionRowComponent(row);
        }

        if (comparer.Any())
            builder.AddEmbed(new DiscordEmbedBuilder()
                .WithImageUrl(actor.Avatar.BodyUrl)
                .WithDescription(stringBuilder.ToString())
                .WithColor(new(await Hub.GetEmbedColorAsync())));


        if (builder.Embeds.Count >= 4 || json)
        {
            using var mem = new MemoryStream(Encoding.UTF8.GetBytes(json && comparer.Any() ? comparer.ToJson(Formatting.Indented)! : rawBuilder.ToString()));
            await ctx.TryEditResponseAsync(new DiscordWebhookBuilder()
                .WithContent($"{HubEmoji.Loading} » {MspClientUtil.GetServerDiscordFlag(server)} » **We have too much tracking information for `{username}` here is a file**")
                .AddFile($"tracking-{user.Actor.Username}-{user.Actor.Server}.txt", mem));
            return;
        }
        await ctx.TryEditResponseAsync(builder);
    }
}

public class TrackingComporer : IEqualityComparer<PixiTrackingMemberStats>
{
    public bool Equals(PixiTrackingMemberStats? x, PixiTrackingMemberStats? y) => y != null &&
                                                                                  x != null &&
                                                                                  x.Actor.Level == y.Actor.Level &&
                                                                                  x.Actor.Fame == y.Actor.Fame &&
                                                                                  x.Actor.StarCoins == y.Actor.StarCoins &&
                                                                                  x.Actor.Diamonds == y.Actor.Diamonds &&
                                                                                  x.Actor.Friends == y.Actor.Friends &&
                                                                                  x.Actor.ProfileViews == y.Actor.ProfileViews;

    public int GetHashCode(PixiTrackingMemberStats obj) => obj.Actor.Level.GetHashCode() ^
                                                           obj.Actor.Fame.GetHashCode() ^
                                                           obj.Actor.StarCoins.GetHashCode() ^
                                                           obj.Actor.Diamonds.GetHashCode() ^
                                                           obj.Actor.Friends.GetHashCode() ^
                                                           obj.Actor.ProfileViews.GetHashCode();
}