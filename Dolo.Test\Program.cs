using Dolo.Core.Interceptor.Models;
using Dolo.Core.Interceptor;
using Dolo.Core.Interceptor.Extensions;
using Dolo.Core.Interceptor.Interfaces;
using Dolo.Core.Extension;
using Dolo.Core;

namespace Dolo.Test;

public partial class Program {
    public static async Task Main(string[] args) {
        await PrintMspAync();
        return;
        var interceptorSettings = new InterceptorConfig()
          .ForMovieStarPlanet()
          .UsePort(8889)
          .UseLogger(a => a.AddTransaction().AddConsole())
          .UseOCSP()
          .EnableAllBreakpoints()
          .AddBreakpointRule(".*mspapis\\.com.*", BreakpointType.Both);

        Console.WriteLine("Creating interceptor...");
        using var interceptor = new HttpsInterceptor(interceptorSettings);
        Console.WriteLine("Interceptor created successfully");

        interceptor.TransactionCompleted += OnTransactionCompletedAsync;
        interceptor.BreakpointHit += OnBreakpointHitAsync;

        Console.WriteLine("🚀 Starting HTTPS Interceptor...");
        await interceptor.StartAsync();
        Console.WriteLine("✅ HTTPS Interceptor Started");
        Console.WriteLine($"� Proxy: 127.0.0.1:{interceptorSettings.ProxyPort}");
        Console.WriteLine("🔍 Waiting for traffic... (Press Ctrl+C to stop)");
        Console.WriteLine();

        Console.CancelKeyPress += async (s, e) => {
            e.Cancel = true;
            Console.WriteLine("\n🛑 Shutting down...");
            await interceptor.StopAsync();
            Environment.Exit(0);
        };

        await Task.Delay(-1);
    }

    private static readonly object ConsoleLock = new();
    private static readonly Dictionary<string, DateTime> RequestTimestamps = new();
    private static readonly Dictionary<string, string> RequestMethods = new();
    private static int _transactionCounter = 0;

    private static async Task OnTransactionCompletedAsync(object? sender, HttpTransactionCompletedEventArgs e) {
        var transaction = e.Transaction;
        var request = transaction.Request;
        var response = transaction.Response;

        lock (ConsoleLock) {
            var transactionNum = Interlocked.Increment(ref _transactionCounter);
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");

            // Generate correlation ID for pairing validation
            var correlationId = $"{transaction.Hostname}:{transaction.Port}:{transactionNum:D4}";

            Console.WriteLine($"\n[{timestamp}] ═══ TRANSACTION #{transactionNum:D4} ═══");
            Console.WriteLine($"🔗 Correlation ID: {correlationId}");
            Console.WriteLine($"🌐 {transaction.Hostname}:{transaction.Port} | ⏱️ {transaction.Duration.TotalMilliseconds:F1}ms");

            // Display REQUEST with pairing validation
            if (request != null) {
                var requestKey = $"{request.Method}:{request.Uri}";
                RequestTimestamps[correlationId] = transaction.StartTime;
                RequestMethods[correlationId] = request.Method ?? "UNKNOWN";

                Console.WriteLine($"📤 REQUEST - {request.Method} {request.Uri}");
                Console.WriteLine($"🕐 Request Time: {transaction.StartTime:HH:mm:ss.fff}");
                Console.WriteLine($"🔗 Request ID: {transaction.TransactionId}");

                if (request.IsAmf) {
                    Console.WriteLine("🔥 AMF REQUEST DETECTED!");
                    if (request.Amf?.DecodedContent?.Content != null) {
                        var jsonContent = request.Amf.DecodedContent.Content.ToJson();
                        Console.WriteLine($"   └─ AMF Content: {jsonContent?.Truncat(500) ?? "[Not serializable]"}");
                    }
                    else if (!string.IsNullOrEmpty(request.Amf?.Error)) {
                        Console.WriteLine($"   └─ AMF Error: {request.Amf.Error}");
                    }
                }
            }

            // Display RESPONSE with pairing validation
            if (response != null) {
                Console.WriteLine($"📥 RESPONSE - {response.StatusCode} {response.ReasonPhrase}");
                Console.WriteLine($"🕐 Response Time: {transaction.EndTime:HH:mm:ss.fff}");
                Console.WriteLine($"🔗 Response ID: {transaction.TransactionId}");

                // Validate pairing correctness
                if (RequestTimestamps.ContainsKey(correlationId) && RequestMethods.ContainsKey(correlationId)) {
                    var requestTime = RequestTimestamps[correlationId];
                    var requestMethod = RequestMethods[correlationId];
                    var pairingDelay = transaction.EndTime - requestTime;

                    Console.WriteLine($"✅ PAIRING VALIDATED: {requestMethod} request paired correctly");
                    Console.WriteLine($"⏱️ Total Round-trip: {pairingDelay?.TotalMilliseconds:F1}ms");
                } else {
                    Console.WriteLine($"❌ PAIRING ERROR: No matching request found for response!");
                }

                if (response.IsAmf) {
                    Console.WriteLine("🔥 AMF RESPONSE DETECTED!");
                    if (response.Amf?.DecodedContent?.Content != null) {
                        var jsonContent = response.Amf.DecodedContent.Content.ToJson();
                        Console.WriteLine($"   └─ AMF Content: {jsonContent?.Truncat(500) ?? "[Not serializable]"}");
                    }
                    else if (!string.IsNullOrEmpty(response.Amf?.Error)) {
                        if (response.Amf.Error == "null") {
                            Console.WriteLine($"   └─ AMF Content: null");
                        }
                        else {
                            Console.WriteLine($"   └─ AMF Decode Error: {response.Amf.Error}");
                        }
                    }
                }
            }
        }

        await Task.CompletedTask;
    }

    private static Task OnBreakpointHitAsync(object? sender, BreakpointHitEventArgs e) {
        lock (ConsoleLock) {
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            Console.WriteLine($"\n[{timestamp}] 🔴 BREAKPOINT HIT!");
            Console.WriteLine($"🆔 Breakpoint ID: {e.BreakpointId}");
            Console.WriteLine($"🌐 URL: {e.Url}");
            Console.WriteLine($"📍 Type: {e.Type}");
            Console.WriteLine($"🔗 Connection: {e.ConnectionId}");
            Console.WriteLine($"🏠 Host: {e.Hostname}:{e.Port}");
            Console.WriteLine($"🔒 Protocol: {e.Protocol}");

            if (e.Request != null) {
                Console.WriteLine($"📤 REQUEST: {e.Request.Method} {e.Request.Uri}");
                if (e.Request.IsAmf && e.Request.Amf?.DecodedContent?.Content != null) {
                    var jsonContent = e.Request.Amf.DecodedContent.Content.ToJson();
                    Console.WriteLine($"   └─ AMF Request: {jsonContent?.Truncat(200) ?? "[Not serializable]"}");
                }
            }

            if (e.Response != null) {
                Console.WriteLine($"📥 RESPONSE: {e.Response.StatusCode} {e.Response.ReasonPhrase}");
                if (e.Response.IsAmf && e.Response.Amf?.DecodedContent?.Content != null) {
                    var jsonContent = e.Response.Amf.DecodedContent.Content.ToJson();
                    Console.WriteLine($"   └─ AMF Response: {jsonContent?.Truncat(200) ?? "[Not serializable]"}");
                }
            }

            Console.WriteLine("🎯 Actions: [C]ontinue, [X]Cancel, [E]xecute");
            Console.Write($"Choose action for {e.BreakpointId[..8]}...: ");
        }

        // Start a background task to handle user input and resolve the breakpoint
        _ = Task.Run(async () => {
            try {
                // Add a small delay to ensure console output is complete
                await Task.Delay(100);

                // Read user input for breakpoint action with timeout
                var inputTask = Task.Run(() => Console.ReadLine());
                var timeoutTask = Task.Delay(30000); // 30 second timeout

                var completedTask = await Task.WhenAny(inputTask, timeoutTask);

                BreakpointAction action;
                if (completedTask == inputTask) {
                    var input = await inputTask;
                    action = input?.ToUpperInvariant() switch {
                        "C" or "CONTINUE" => BreakpointAction.Continue,
                        "X" or "CANCEL" => BreakpointAction.Cancel,
                        "E" or "EXECUTE" => BreakpointAction.Execute,
                        _ => BreakpointAction.Continue // Default to continue
                    };
                }
                else {
                    // Timeout - auto-continue
                    action = BreakpointAction.Continue;
                    lock (ConsoleLock) {
                        Console.WriteLine($"⏰ Timeout for {e.BreakpointId[..8]}... - auto-continuing");
                    }
                }

                lock (ConsoleLock) {
                    Console.WriteLine($"✅ User chose: {action} for {e.BreakpointId[..8]}...");
                    Console.WriteLine();
                }

                // Resolve the breakpoint directly through the event args
                e.ResolveBreakpoint(action);
            }
            catch (Exception ex) {
                lock (ConsoleLock) {
                    Console.WriteLine($"❌ Error handling breakpoint input: {ex.Message}");
                }

                // Auto-continue on error
                e.ResolveBreakpoint(BreakpointAction.Continue);
            }
        });

        // Return immediately - let the BreakpointManager wait for ActionSource.Task
        return Task.CompletedTask;
    }


}
