using Dolo.Bot.Pixi.Hub.Global.Enums;
using Dolo.Core;
using Dolo.Core.Extension;
using Dolo.Nebula;
using Dolo.Nebula.Entities;
using Dolo.Nebula.Enum;
using Dolo.Planet;
using Dolo.Planet.Entities;
using Dolo.Planet.Entities.Theme;
using Dolo.Planet.Enums;
using Dolo.Pluto.Shard.Bot.Pixi;
using System.Text;

namespace Dolo.Bot.Pixi.Hub;

public static class HubEmbed
{
    public static IEnumerable<DiscordActionRowComponent> _buttons(ulong id, List<DiscordComponent>? rows = null)
    {
        var components = new List<DiscordComponent>();

        // Add buttons with emojis if available, otherwise without emojis
        var giftEmoji = HubEmoji.Gift;
        if (giftEmoji is not null)
            components.Add(new DiscordButtonComponent(DiscordButtonStyle.Danger, "btn-changelog", "Mailbox", false, new DiscordComponentEmoji(giftEmoji)));
        else
            components.Add(new DiscordButtonComponent(DiscordButtonStyle.Danger, "btn-changelog", "Mailbox"));

        var loadingEmoji = HubEmoji.Loading;
        if (loadingEmoji is not null)
            components.Add(new DiscordButtonComponent(DiscordButtonStyle.Primary, "btn-commands", "Commands", false, new DiscordComponentEmoji(loadingEmoji)));
        else
            components.Add(new DiscordButtonComponent(DiscordButtonStyle.Primary, "btn-commands", "Commands"));

        var linkComponents = new List<DiscordComponent>();

        var astroEmoji = HubEmoji.Astro;
        if (astroEmoji is not null)
            linkComponents.Add(new DiscordLinkButtonComponent(Hub.BotInviteUrl, "Add Bot", false, new DiscordComponentEmoji(astroEmoji)));
        else
            linkComponents.Add(new DiscordLinkButtonComponent(Hub.BotInviteUrl, "Add Bot"));

        var mspHeartEmoji = HubEmoji.MspHeart;
        if (mspHeartEmoji is not null)
            linkComponents.Add(new DiscordLinkButtonComponent("https://discord.gg/mspshop", "Shop", false, new DiscordComponentEmoji(mspHeartEmoji)));
        else
            linkComponents.Add(new DiscordLinkButtonComponent("https://discord.gg/mspshop", "Shop"));

        return new List<DiscordActionRowComponent>
        {
            new DiscordActionRowComponent(components),
            new DiscordActionRowComponent(linkComponents)
        };
    }

    /// <summary>
    ///     Embed for cloth
    /// </summary>
    public static async Task<DiscordWebhookBuilder> ClothAsync(DiscordGuild guild, MspActor actor)
    {
        var embed = new DiscordWebhookBuilder();
        foreach (var row in _buttons(guild.Id))
        {
            embed.AddActionRowComponent(row);
        }

        if (!actor.ClothRel.Any())
            return embed;

        var embedBuilder = new DiscordEmbedBuilder()
            .WithAuthor(actor.Username, string.Empty, actor.Avatar.AvatarUrl)
            .WithThumbnail(actor.Avatar.AvatarUrl)
            .WithColor(new(await Hub.GetEmbedColorAsync()));

        var rawBuilder = new StringBuilder();
        var strBuilder = new StringBuilder();
        foreach (var cloth in actor.ClothRel)
        {
            rawBuilder.AppendLine($"Id » {cloth?.Cloth?.Id}");
            rawBuilder.AppendLine($"Name » {cloth?.Cloth?.Name}");
            rawBuilder.AppendLine($"Category » {cloth?.Cloth?.Category?.Name}");
            rawBuilder.AppendLine($"Price » {cloth?.Cloth?.GetPriceNoMarkdown()}");
            rawBuilder.AppendLine($"Rare » {(cloth?.Cloth?.ShopId == -100 ? "Yes" : "No")}");
            rawBuilder.AppendLine();

            strBuilder.AppendLine($"{HubEmoji.Astro} **»** [Id](https://a) » **{cloth?.Cloth?.Id}**");
            strBuilder.AppendLine($"{HubEmoji.Astro} **»** [Name](https://a) » **{cloth?.Cloth?.Name}**");
            strBuilder.AppendLine($"{HubEmoji.Astro} **»** [Category](https://a) » **{cloth?.Cloth?.Category?.Name}**");
            strBuilder.AppendLine($"{HubEmoji.Astro} **»** [Price](https://a) » {cloth?.Cloth?.GetPrice()}");
            strBuilder.AppendLine($"{HubEmoji.Astro} **»** [Rare](https://a) » {(cloth?.Cloth?.ShopId == -100 ? HubEmoji.Yes : HubEmoji.No)}");
            strBuilder.AppendLine();
        }

        if (strBuilder.Length <= 4096)
        {
            embedBuilder.WithDescription(strBuilder.ToString());
            embedBuilder.WithImageUrl(actor.Avatar.BodyUrl);
            embed.AddEmbed(embedBuilder);
            return embed;
        }

        embed.Clear();
        embed.ClearComponents();
        embed.AddFile($"{actor.Username}-{actor.Server}.txt", new MemoryStream(Encoding.UTF8.GetBytes(rawBuilder.ToString())));
        return embed;
    }

    /// <summary>
    ///     Embed for Query command result
    /// </summary>
    public static async Task<DiscordWebhookBuilder> QueryAsync(DiscordMember member, DiscordGuild guild, MspActor actor, bool isExtended = false, bool isLoading = false, NebulaClient? nebula = null) {
        try {

            var trackerUser = await HubTracking.TryGetMemberAsync(actor.ProfileId);
            var hasTrackingData = trackerUser != null && trackerUser.Stats.Any(a => a.Actor.Fame != 0 && a.Actor.StarCoins != 0);
            var hasCheater = await Hub.IsCheaterAsync(actor.ProfileId) && !await Hub.IsCheaterWhitelistAsync(actor.ProfileId);
            var buttonDisabled = hasCheater || await Hub.IsUserBlockedAsync(member.Id) || await Hub.IsMemberBannedAsync(member.Id) || await Hub.IsCheaterWhitelistAsync(actor.ProfileId);
            var buttons = _buttons(guild.Id, new());
            var embedColor = new DiscordColor(hasCheater ? "#EA5595" : actor.IsVip ? "#FCCE39" : await Hub.GetEmbedColorAsync());
            var statusText = isLoading
                ? "Loading.."
                : (actor.Status is { HasStatus: true }
                    ? $"**Status has [{actor.Status.Likes:N0}](https://a) likes**\n```{actor.Status.Text?.ReplaceAdvertisementStatus()}```"
                    : string.Empty);

            actor.Fame = hasTrackingData ? trackerUser!.Stats.Last().Actor.Fame : actor.Fame;

            ProfileIdentityData? identity = nebula is null ? null : await nebula.GetProfileIdentityAsync(actor.ProfileId);
            var embed = new DiscordWebhookBuilder();
            foreach (var row in buttons) {
                embed.AddActionRowComponent(row);
            }
            embed.AddEmbed(new DiscordEmbedBuilder()
                .WithAuthor(actor.Username, string.Empty, actor.Avatar.AvatarUrl)
                .WithThumbnail(actor.Avatar.AvatarUrl)
                .WithDescription(new StringBuilder()
                    .AppendLine(new StringBuilder()
                        .Append(actor.IsVip ? HubEmoji.Vip?.ToString() : string.Empty)
                        .Append(actor.IsJudge ? HubEmoji.Judge?.ToString() : string.Empty)
                        .Append(actor.IsJury ? HubEmoji.Jury?.ToString() : string.Empty)
                        .Append(actor.IsCeleb ? HubEmoji.Celeb?.ToString() : string.Empty).ToString())
                    .AppendLine(statusText)
                    .AppendLine($"💃🏻 **»** [Animation](https://a) » **{(isLoading ? "Loading..." : (string.IsNullOrEmpty(actor.Status.FigureAnimation) ? HubEmoji.No : actor.Status.FigureAnimation?.Replace("_", " ")))}**")
                    .AppendLine($"<:dolo_nebula:748575942481543280> **»** [ProfileId](https://a) » **{actor.ProfileId ?? HubEmoji.No}**")
                    .AppendLine($"{MspClientUtil.GetServerDiscordFlag(actor.Server)} **»** [Server](https://a) » **{actor.Server}**")
                    .AppendLine($"<:dolo_user:748575942712098960> **»** [ID](https://a) » **{actor.Id}**")

                    // .AppendLine($"<:dm:806006181244043264> **»** [Diamonds](https://a) » **{actor.Diamonds:N0}**")
                    // .AppendLine($"<:sc:806006180837195777> **»** [StarCoins](https://a) » **{actor.StarCoins:N0}**")
                    // .AppendLine($"<:fortune:806006180743741452> **»** [Fortune](https://a) » **{actor.Fortune:N0}**")
                    // .AppendLine($"<:fame:806006180707041291> **»** [Fame](https://a) » **{actor.Fame:N0}** {(actor.Level < 101 ? $" » `{actor.GetPercentage()} / 100%` » `{actor.FameUntilNextLevel.To<ulong>():N0}`" : string.Empty)}")
                    .AppendLine($"<:dm:806006181244043264> **»** [Diamonds](https://a) » {(hasTrackingData ? $"**{trackerUser?.Stats.Last().Actor.Diamonds:N0}**" : "*unknown*")}")
                    .AppendLine($"<:sc:806006180837195777> **»** [StarCoins](https://a) » {(hasTrackingData ? $"**{trackerUser?.Stats.Last().Actor.StarCoins:N0}**" : "*unknown*")}")
                    .AppendLine($"<:fortune:806006180743741452> **»** [Fortune](https://a) » {(hasTrackingData ? $"**{trackerUser?.Stats.Last().Actor.Fortune:N0}**" : "*unknown*")}")
                    .AppendLine($"<:fame:806006180707041291> **»** [Fame](https://a) » **{(hasTrackingData ? $"{trackerUser?.Stats.Last().Actor.Fame:N0}**" : "*unknown*")} {(actor.Level < 101 ? $" » `{actor.GetPercentage()} / 100%` » {(hasTrackingData ? $"`{trackerUser?.Stats.Last().Actor.FameUntilNextLevel.To<ulong>():N0}`" : "*unknown*")}" : string.Empty)}")

                    .AppendLine($"<:lvl:806006181075222538> **»** [Level](https://a) » **{actor.Level}**")
                    .AppendLine()
                    .AppendLine($"<:dolo_color:748575942556909619> **»** [Eye-ShadowColor](https://a) » **{(actor.Beauty?.EyeShadow is null ? HubEmoji.No : actor.Beauty.EyeShadow.Color)}**")
                    .AppendLine($"<:dolo_eyeshadow:748575942443794545> **»** [Eye-ShadowId](https://a) » **{(actor.Beauty?.EyeShadow is null ? HubEmoji.No : actor.Beauty.EyeShadow.Id)}**")
                    .AppendLine($"<:dolo_color:748575942556909619> **»** [Mouth-Color](https://a) » **{actor.Beauty?.Mouth?.Color ?? HubEmoji.No}**")
                    .AppendLine($"<:dolo_skin:748575942619955220> **»** [Skin-Color](https://a) » **{actor.Beauty?.Skincolor}**")
                    .AppendLine($"<:dolo_color:748575942556909619> **»** [Eye-Color](https://a) » **{actor.Beauty?.Eye?.Color ?? HubEmoji.No}**")
                    .AppendLine($"<:dolo_lips:748575942796115968> **»** [Mouth-Id](https://a) » **{actor.Beauty?.Mouth?.Id}**")
                    .AppendLine($"<:dolo_nose:748575942246531114> **»** [Nose-Id](https://a) » **{actor.Beauty?.Nose?.Id}**")
                    .AppendLine($"<:dolo_eye:748575942267502696> **»** [Eye-Id](https://a) » **{actor.Beauty?.Eye?.Id}**")
                    .AppendLine()
                    .AppendLine($"⛔ **»** [Deleted](https://a) » {(actor.IsDeleted ? HubEmoji.Yes : HubEmoji.No)}")
                    .AppendLine($"<:dolo_friends:748575942682738808> **»** [Friends](https://a) » {(hasTrackingData ? $"**{trackerUser?.Stats.Last().Actor.Friends:N0}**" : "*unknown*")} **/ {actor.MaxFriends:N0}**")
                    .AppendLine($"<:dolo_vip:748575942829539358> **»** [VIP-Friends](https://a) » {(hasTrackingData ? $"**{trackerUser?.Stats.Last().Actor.FriendsVip:N0}**" : "*unknown*")}")
                    .AppendLine($"<:dolo_totalvip:748592337692917881> **»** [VIP-Days](https://a) » {(hasTrackingData ? $"**{trackerUser?.Stats.Last().Actor.TotalVipDays:N0}**" : "*unknown*")} {(actor.IsVip ? $"<t:{actor.MembershipPurchasedAt.ToTimestamp()}>" : string.Empty)}")
                    .AppendLine($"<:dolo_lastlogin:748591896095490049> **»** [Last-Login](https://a) » {(isLoading ? "Loading.." : $"<t:{identity?.GetLastLogin(GameType.MovieStarPlanet).ToTimestamp()}>")}")
                    .AppendLine($"<:dolo_lastlogin:748591896095490049> **»** [CreatedAt](https://a) » {(isLoading ? "Loading.." : $"<t:{actor.CreatedAt.GetValueOrDefault().ToTimestamp()}>")}")
                    .AppendLine($"<:dolo_lastlogin:748591896095490049> **»** [Inactive-Since](https://a) » {(isLoading ? "Loading.." : $"<t:{identity?.GetLastLogin(GameType.MovieStarPlanet).ToTimestamp()}:R>")}")
                    .AppendLine($"<:dolo_view:886675157832318976> **»** [Profile-Views](https://a) » **{actor.ProfileViews:N0}**")
                    .ToString())
                .WithColor(embedColor));

            if (isExtended)
                embed.AddEmbed(isLoading
                    ? new DiscordEmbedBuilder()
                        .WithDescription("Loading..")
                        .WithColor(new(hasCheater ? "#EA5595" : await Hub.GetEmbedColorAsync()))
                    : new DiscordEmbedBuilder()
                        .WithDescription(new StringBuilder()
                            .AppendLine($"{HubEmoji.Astro} **»** [Rares](https://a) » **{actor.Rares.Count:N0}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [Artbooks](https://a) » **{actor.Artbooks.Count:N0}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [Guestbooks](https://a) » **{actor.Guestbooks.Count:N0}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [Looks](https://a) » **{actor.Looks.Count:N0}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [Clothes](https://a) » **{actor.Inventory.Count:N0}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [Statuses](https://a) » **{actor.Statuses.Count:N0}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [Pictures](https://a) » **{actor.Pictures.Count:N0}**")
                            .AppendLine($"{HubEmoji.Astro} **»** [Room-Likes](https://a) » **{(actor.Room is null ? 0 : actor.Room.Likes.ToString("N0"))}**")
                            .ToString())
                        .WithColor(new(hasCheater ? "#EA5595" : await Hub.GetEmbedColorAsync())));

            embed.AddEmbed(new DiscordEmbedBuilder()
                .WithDescription(new StringBuilder()
                    .AppendLine(actor.GetClothDiscordData())
                    .AppendLine("**Outfit-Price**")
                    .AppendLine(actor.OutfitPriceDiscord)
                    .ToString())
                .WithImageUrl(actor.Avatar.BodyUrl)
                .WithColor(embedColor));

            if (hasCheater)
                embed.AddEmbed(new DiscordEmbedBuilder()
                    .WithColor(embedColor)
                    .WithDescription("**We have received multiple reports that this user have cheated on moviestarplanet. After conducting an investigation, we can confirm that these reports are true.**")
                    .WithTitle("Reported Actor")
                    .WithImageUrl("https://cdn.discordapp.com/attachments/944722654907219988/1056998179482124338/cheater.png"));

            if (await Hub.IsShopAsync())
                embed.AddEmbed(new DiscordEmbedBuilder {
                    Color = embedColor,
                    Description = new StringBuilder()
                        .AppendLine("## NEW Shop")
                        .AppendLine("Checkout our official shop, with the best tools.")
                        .AppendLine("Custom tools are also available.")
                        .AppendLine()
                        .AppendLine("`\ud83c\udf81` [**Open Shop **](https://discord.gg/mspshop)")
                        .ToString(),
                });
            return embed;
        }
        catch (Exception exception) {
            Console.WriteLine(exception.ToJson());
        }

        return null;
    }

    /// <summary>
    ///     Embed for help command
    /// </summary>
    public static async Task<DiscordWebhookBuilder> HelpAsync(DiscordGuild guild)
    {
        var embed = new DiscordWebhookBuilder();
        foreach (var row in _buttons(guild.Id))
        {
            embed.AddActionRowComponent(row);
        }
        embed.AddEmbed(new DiscordEmbedBuilder()
            .WithImageUrl($"{Hub.Pluto?.BannerUrl}?size=1024")
            .WithDescription(new StringBuilder()
                .AppendLine("### Basic Commands")
                .AppendLine($"{HubEmoji.Astro} </msp help:1263044051217678346> » Display all available commands")
                .AppendLine($"{HubEmoji.Astro} </msp query:1263044051217678346> » Query user information")
                .AppendLine($"{HubEmoji.Astro} </msp id:1263044051217678346> » Get user ID")
                .AppendLine($"{HubEmoji.Astro} </msp name:1263044051217678346> » Get username")
                .AppendLine("### Look & Inventory")
                .AppendLine($"{HubEmoji.Astro} </msp look:1263044051217678346> » View user's look")
                .AppendLine($"{HubEmoji.Astro} </msp inventory:1263044051217678346> » View complete inventory")
                .AppendLine($"{HubEmoji.Astro} </msp preview:1263044051217678346> » Preview clothing item")
                .AppendLine($"{HubEmoji.Astro} </msp outfit:1263044051217678346> » Get outfit details")
                .AppendLine("### Media & Content")
                .AppendLine($"{HubEmoji.Astro} </msp pic:1263044051217678346> » Get random pictures")
                .AppendLine($"{HubEmoji.Astro} </msp room:1263044051217678346> » View room picture")
                .AppendLine($"{HubEmoji.Astro} </msp anim:1263044051217678346> » Search animations")
                .AppendLine($"{HubEmoji.Astro} </msp theme:1263044051217678346> » View latest/upcoming themes")
                .AppendLine("### Statistics & Info")
                .AppendLine($"{HubEmoji.Astro} </msp track:1263044051217678346> » View tracking information")
                .AppendLine($"{HubEmoji.Astro} </msp highscore:1263044051217678346> » View top 25 rankings")
                .AppendLine($"{HubEmoji.Astro} </msp availability:1263044051217678346> » Check server availability")
                .AppendLine($"{HubEmoji.Astro} </msp chat:1263044051217678346> » View chat-lock information")
                .AppendLine($"{HubEmoji.Astro} </msp check:1263044051217678346> » Check server connections")
                .AppendLine($"{HubEmoji.Astro} </msp info:1263044051217678346> » Bot information")
                .ToString())
            .WithColor(new(await Hub.GetEmbedColorAsync())));

        return embed;
    }

    /// <summary>
    ///     Embed for checking servers
    /// </summary>
    public static async Task<DiscordWebhookBuilder> CheckAsync(DiscordGuild guild, StringBuilder builder)
    {
        var embed = new DiscordWebhookBuilder();
        foreach (var row in _buttons(guild.Id))
        {
            embed.AddActionRowComponent(row);
        }
        embed.AddEmbed(new DiscordEmbedBuilder()
            .WithTitle($"Server-Checks {HubEmoji.MspHeart}")
            .WithDescription(builder.ToString())
            .WithColor(new(await Hub.GetEmbedColorAsync())));

        return embed;
    }

    /// <summary>
    ///     Embed for checking chat-lock
    /// </summary>
    public static async Task<DiscordWebhookBuilder> ChatAsync(DiscordGuild guild, StringBuilder builder)
    {
        var embed = new DiscordWebhookBuilder();
        foreach (var row in _buttons(guild.Id))
        {
            embed.AddActionRowComponent(row);
        }
        embed.AddEmbed(new DiscordEmbedBuilder()
            .WithTitle($"Chat-locks {HubEmoji.MspHeart}")
            .WithDescription(builder.ToString())
            .WithColor(new(await Hub.GetEmbedColorAsync()))
            .WithImageUrl("https://cdn.discordapp.com/attachments/944722654907219988/1056626505226129428/Video.gif"));

        return embed;
    }

    /// <summary>
    ///     Embed for availability
    /// </summary>
    public static async Task<DiscordWebhookBuilder> AvailabilityAsync(DiscordGuild guild, StringBuilder builder, int count)
    {
        var embed = new DiscordWebhookBuilder();
        foreach (var row in _buttons(guild.Id))
        {
            embed.AddActionRowComponent(row);
        }
        embed.AddEmbed(new DiscordEmbedBuilder()
            .WithTitle($"Available on {count:N0} {"server".Pluralize(count)} {HubEmoji.MspHeart}")
            .WithDescription(builder.ToString())
            .WithColor(new(await Hub.GetEmbedColorAsync())));

        return embed;
    }

    /// <summary>
    ///     Embed for highscore
    /// </summary>
    public static async Task<DiscordWebhookBuilder> HighscoreAsync(DiscordGuild guild, StringBuilder builder)
    {
        var embed = new DiscordWebhookBuilder();
        foreach (var row in _buttons(guild.Id))
        {
            embed.AddActionRowComponent(row);
        }
        embed.AddEmbed(new DiscordEmbedBuilder()
            .WithTitle($"Top 25 {HubEmoji.MspHeart}")
            .WithDescription(builder.ToString())
            .WithColor(new(await Hub.GetEmbedColorAsync())));

        return embed;
    }

    /// <summary>
    ///     Embed for picture
    /// </summary>
    public static async Task<DiscordWebhookBuilder> PictureAsync(DiscordGuild guild, MspPicture picture, Dolo.Planet.Enums.Server server) {
        var embed = new DiscordWebhookBuilder();
        foreach (var row in _buttons(guild.Id))
        {
            embed.AddActionRowComponent(row);
        }
        embed.AddEmbed(new DiscordEmbedBuilder()
            .WithTitle("Picture")
            .WithImageUrl(picture.PictureUrl)
            .WithThumbnail(picture.AvatarUrl)
            .WithDescription(new StringBuilder()
                .AppendLine($"```{picture.Name}```")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [User](https://a) » {picture.Username}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Server](https://a) » {server}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Likes](https://a) » {picture.Likes:N0}**")
                .ToString())
            .WithColor(new(await Hub.GetEmbedColorAsync())));

        return embed;
    }

    /// <summary>
    ///     Embed for animation
    /// </summary>
    public static async Task<DiscordWebhookBuilder> AnimationAsync(DiscordGuild guild, MspAnimation animation)
    {
        var embed = new DiscordWebhookBuilder();
        foreach (var row in _buttons(guild.Id))
        {
            embed.AddActionRowComponent(row);
        }
        embed.AddEmbed(new DiscordEmbedBuilder()
            .WithTitle("Animation")
            .WithImageUrl(animation.GetUrl(Gender.Female))
            .WithDescription(new StringBuilder()
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Name](https://a) » {animation.Name}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Category](https://a) » {animation.CategoryName}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Id](https://a) » {animation.Id}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [IsVip](https://a) » {(animation.IsVip ? HubEmoji.Yes : HubEmoji.No)}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Price](https://a) » {(animation.Price != 0 ? $"<:sc:806006180837195777> {animation.Price:N0}" : $"<:dm:806006181244043264> {animation.DiamondsPrice:N0}")}**")
                .ToString())
            .WithColor(new(await Hub.GetEmbedColorAsync())));

        return embed;
    }

    /// <summary>
    ///     Embed for theme
    /// </summary>
    public static async Task<DiscordWebhookBuilder> ThemeAsync(DiscordGuild guild, MspThemeContent theme, ThemeType type)
    {
        var embed = new DiscordWebhookBuilder();
        foreach (var row in _buttons(guild.Id))
        {
            embed.AddActionRowComponent(row);
        }
        embed.AddEmbed(new DiscordEmbedBuilder()
            .WithTitle(type == ThemeType.Latest ? "Latest Theme" : "Upcoming Theme")
            .WithImageUrl(type == ThemeType.Latest ? theme.Latest?.Url : theme.Upcoming?.Url)
            .WithDescription(new StringBuilder()
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Name](https://a) » {(type == ThemeType.Latest ? theme.Latest?.Name : theme.Upcoming?.Name)}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Id](https://a) » {(type == ThemeType.Latest ? theme.Latest?.Id : theme.Upcoming?.Id)}**")
                .ToString())
            .WithColor(new(await Hub.GetEmbedColorAsync())));

        return embed;
    }

    /// <summary>
    ///     Embed for information
    /// </summary>
    public static async Task<DiscordWebhookBuilder> InfoAsync(int guilds, int members, DiscordGuild guild)
    {
        var embed = new DiscordWebhookBuilder();
        foreach (var row in _buttons(guild.Id))
        {
            embed.AddActionRowComponent(row);
        }
        embed.AddEmbed(new DiscordEmbedBuilder()
            .WithThumbnail(Hub.Pluto!.IconUrl)
            .WithImageUrl($"{Hub.Pluto.BannerUrl}?size=1024")
            .WithDescription(new StringBuilder()
                .AppendLine("### 🌟 A MovieStarPlanet Bot")
                .AppendLine($"{HubEmoji.MspHeart} **Powerful Features**")
                .AppendLine($"{HubEmoji.MspHeart} **Regular Updates**")
                .AppendLine($"{HubEmoji.MspHeart} **Tracking Commands**")
                .AppendLine($"### Stats")
                .AppendLine($"{HubEmoji.Astro} **Servers** » {guilds:N0}")
                .AppendLine($"{HubEmoji.Astro} **Members** » {members:N0}")
                .AppendLine($"{HubEmoji.Astro} » [Pluto](https://disccord.gg/dolo)")
                .ToString())
            .WithColor(new(await Hub.GetEmbedColorAsync())));

        return embed;
    }

    /// <summary>
    ///     Embed for information
    /// </summary>
    public static async Task<DiscordInteractionResponseBuilder> TermsAsync()
    {
        return new DiscordInteractionResponseBuilder()
            .AsEphemeral()
            .AddActionRowComponent(new DiscordButtonComponent(DiscordButtonStyle.Primary, "terms", "Accept", false, new("📜")))
            .AddEmbed(new DiscordEmbedBuilder()
                .WithImageUrl("https://cdn.discordapp.com/attachments/944722654907219988/119365298944188012 Alessia4.png")
                .WithDescription(new StringBuilder()
                    .AppendLine("## `\ud83d\udcb8` Premium Terms")
                    .AppendLine("- You can purchase a subscription to continue using the features.")
                    .AppendLine("- You can cancel your subscription at any time.")
                    .AppendLine("- You can use the subscription on any servers.")
                    .AppendLine("- Your subscription is managed by Discord")
                    .ToString())
                .WithColor(new("2B2D31")));
    }

    public static DiscordWebhookBuilder Trial(PixiMember member)
    {
        return new DiscordWebhookBuilder()
            .AddEmbed(new DiscordEmbedBuilder()
                .WithImageUrl("https://cdn.discordapp.com/attachments/944722654907219988/1193652989441880124/dc09af54-dddf-4424-87d4-c1b253369514.png")
                .WithDescription(new StringBuilder()
                    .AppendLine("## `\ud83d\udcb8` Cooldown")
                    .AppendLine($"- You can use this premium feature <t:{member.TimeStamp}:R>")
                    .AppendLine("- You can purchase a subscription to continue using the features without limit.")
                    .ToString())
                .WithColor(new("2B2D31")));
    }

    /// <summary>
    ///     Embed for Log Message Execution
    /// </summary>
    public static async Task<DiscordEmbed> LogExecutionsAsync(SlashCommandContext ctx, string command)
    {
        return new DiscordEmbedBuilder()
            .WithTitle("Command")
            .WithThumbnail(ctx.Member.AvatarUrl)
            .WithDescription(new StringBuilder()
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Username](https://a) » {ctx.Member.Username}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [UserId](https://a) » {ctx.Member.Id}**")
                .AppendLine()
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Chanel](https://a) » {ctx.Channel.Name}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Guild](https://a) » {ctx.Guild.Name}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [GuildId](https://a) » {ctx.Guild.Id}**")
                .AppendLine()
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Command](https://a) » {command}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [CreatedAt](https://a) » {ctx.Member.CreationTimestamp}**")
                .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Time](https://a) » {DateTime.Now}**")
                .AppendLine()
                .ToString())
            .WithColor(new(await Hub.GetEmbedColorAsync()));
    }

    /// <summary>
    ///     Embed for Log Message Execution
    /// </summary>
    public static async Task LogAsync(DiscordGuild? guild, DiscordUser member, string message)
    {
        await DiscordWebHooker.SendAsync("https://ptb.discord.com/api/webhooks/1025329798555369553/7_1Zvi3yNne_rIIifjwwsHsXIFuj806zjtV5kxeCeQvAtnJKEihkdnMJf3HIOu85AEKU", "Message", "#AAFDFB", new StringBuilder()
            .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Username](https://a) » {member.Username}**")
            .AppendLine($"{HubEmoji.Astro?.ToString()} **» [UserId](https://a) » {member.Id}**")
            .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Guild](https://a) » {guild?.Name}**")
            .AppendLine($"{HubEmoji.Astro?.ToString()} **» [GuildId](https://a) » {guild?.Id}**")
            .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Time](https://a) » {DateTime.Now}**")
            .AppendLine($"{HubEmoji.Astro?.ToString()} **» [Message](https://a):**")
            .AppendLine($"```{message}```")
            .ToString(), member.AvatarUrl);
    }

    /// <summary>
    ///     Embed for changelog
    /// </summary>
    public static async Task<DiscordWebhookBuilder> ChangelogAsync(DiscordGuild guild)
    {
        var embed = new DiscordWebhookBuilder();
        foreach (var row in _buttons(guild.Id))
        {
            embed.AddActionRowComponent(row);
        }
        embed.AddEmbed(new DiscordEmbedBuilder()
            .WithTitle($"What's New {HubEmoji.MspHeart}")
            .WithThumbnail(Hub.Pluto?.IconUrl)
            .WithDescription(new StringBuilder()
                .AppendLine("# Mailbox 01.07.2025")
                .AppendLine($"{HubEmoji.Gift} **MSP Update**")
                .AppendLine()
                .AppendLine($"{HubEmoji.Astro} **»** msp partially patched the statistics of some properties.")
                .AppendLine($"{HubEmoji.Astro} **»** we are using now a mix of tracked data.")
                .AppendLine()
                .AppendLine()
                .AppendLine("*Use </msp help:1263044051217678346> to see all available commands*")
                .ToString())
            .WithColor(new(await Hub.GetEmbedColorAsync()))
            .WithImageUrl($"{Hub.Pluto?.BannerUrl}?size=1024"));

        return embed;
    }
}