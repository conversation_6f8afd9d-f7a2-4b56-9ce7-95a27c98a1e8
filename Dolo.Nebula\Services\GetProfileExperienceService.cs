using Dolo.Core.Http;
using Dolo.Nebula.Entities;
namespace Dolo.Nebula.Services;

internal class GetProfileExperienceService
{
    public static async Task<ProfileExperienceData?> GetExperienceAsync(NebulaClient client, string? profileid)
    {
        if (string.IsNullOrEmpty(client.Config.Auth))
            throw new NullReferenceException("authkey is required on this method");

        var http = await Http.TrySendAsync<ProfileExperienceData>(a => {
            a.Method = HttpMethod.Get;
            a.Url = $"{client.Services?.GetExperience()}/v1/profiles/{profileid ?? client.Config.GetAccessToken()?.ProfileId}/games/{client.Config.Game.GetValueOrDefault().GetNebulaGame()}/experience";
            a.AuthToken = client.Config.Auth;
        });

        return http.Body;
    }
}
