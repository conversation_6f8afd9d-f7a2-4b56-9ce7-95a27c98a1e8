using Dolo.Core.Extension;
using Dolo.Planet.Entities.Beauty;
using Dolo.Planet.Entities.Cloth;
using Dolo.Planet.Entities.Friend;
using Dolo.Planet.Entities.Internal;
using Dolo.Planet.Entities.Status;
using Dolo.Planet.Enums;
using MongoDB.Bson.Serialization.Attributes;
namespace Dolo.Planet.Entities;

/// <summary>
///     Represents an actor in the MovieStarPlanet game.
/// </summary>
public sealed class MspActor : MspBaseActor
{
    /// <summary>
    ///     Initializes a new instance of the MspActor class.
    /// </summary>
    internal MspActor()
    {
        Looks =
            [];
        Artbooks =
            [];
        Guestbooks =
            [];
        Statuses =
            [];
        Pictures =
            [];
        Inventory =
            [];
        Movies =
            [];
        ClothRel =
            [];
        RelationShips =
            [];
    }

    /// <summary>
    ///     Gets or sets the last login date and time of the actor.
    /// </summary>
    public DateTime LastLoginAt { get; internal set; }

    /// <summary>
    ///     Gets or sets the date and time when the actor purchased the membership.
    /// </summary>
    public DateTime MembershipPurchasedAt { get; internal set; }

    /// <summary>
    ///     Gets or sets the date and time when the actor's membership will timeout.
    /// </summary>
    public DateTime MembershipTimeoutAt { get; internal set; }

    /// <summary>
    ///     Gets or sets the date and time when the actor was created.
    /// </summary>
    public DateTime? CreatedAt { get; internal set; }

    /// <summary>
    ///     Gets or sets the VIP tier of the actor.
    /// </summary>
    public VipTierType VipTier { get; internal set; }

    /// <summary>
    ///     Gets or sets the friend status of the actor.
    /// </summary>
    public FriendStatusType FriendStatus { get; internal set; }

    /// <summary>
    ///     Gets or sets the gender of the actor.
    /// </summary>
    public Gender Gender { get; internal set; }

    /// <summary>
    ///     Gets or sets the server of the actor.
    /// </summary>
    public Server Server { get; internal set; }

    /// <summary>
    ///     Gets or sets the avatar of the actor.
    /// </summary>
    public MspAvatar Avatar { get; internal set; } = new();

    /// <summary>
    ///     Gets or sets the beauty of the actor.
    /// </summary>
    public MspBeauty? Beauty { get; internal set; }

    /// <summary>
    ///     Gets or sets the status of the actor.
    /// </summary>
    public new MspStatus Status { get; internal set; } = new();

    /// <summary>
    ///     Gets or sets the looks of the actor.
    /// </summary>
    [JsonIgnore]
    [BsonIgnore]
    public MspList<MspLook> Looks { get; internal set; }

    /// <summary>
    ///     Gets or sets the artbooks of the actor.
    /// </summary>
    [JsonIgnore]
    [BsonIgnore]
    public MspList<MspArtbook> Artbooks { get; internal set; }

    /// <summary>
    ///     Gets or sets the guestbooks of the actor.
    /// </summary>
    [JsonIgnore]
    [BsonIgnore]
    public MspList<MspWallPost> Guestbooks { get; internal set; }

    /// <summary>
    ///     Gets or sets the statuses of the actor.
    /// </summary>
    [JsonIgnore]
    [BsonIgnore]
    public MspList<MspWallPostActivities> Statuses { get; internal set; }

    /// <summary>
    ///     Gets or sets the pictures of the actor.
    /// </summary>
    [JsonIgnore]
    [BsonIgnore]
    public MspList<MspPicture> Pictures { get; internal set; }

    /// <summary>
    ///     Gets or sets the inventory of the actor.
    /// </summary>
    [JsonIgnore]
    [BsonIgnore]
    public MspList<MspActorCloth> Inventory { get; internal set; }

    /// <summary>
    ///     Gets or sets the movies of the actor.
    /// </summary>
    [JsonIgnore]
    [BsonIgnore]
    public MspList<MspMovie> Movies { get; internal set; }

    /// <summary>
    ///     Gets or sets the room of the actor.
    /// </summary>
    public MspRoom? Room { get; internal set; }

    /// <summary>
    ///     Gets or sets the cloth relationship of the actor.
    /// </summary>
    [JsonIgnore]
    [BsonIgnore]
    public List<MspClothRel?> ClothRel { get; internal set; }

    /// <summary>
    ///     Gets the relationships of the actor.
    /// </summary>
    public List<MspSpecialFriend> RelationShips { get; }

    /// <summary>
    ///     Gets or sets the profile ID of the actor.
    /// </summary>
    public string? ProfileId { get; internal set; }

    /// <summary>
    ///     Gets or sets the room URL of the actor.
    /// </summary>
    public string? RoomUrl { get; internal set; }

    /// <summary>
    ///     Gets or sets the username of the actor.
    /// </summary>
    public string? Username { get; internal set; }

    /// <summary>
    ///     Gets or sets the ID of the actor.
    /// </summary>
    public int Id { get; internal set; }

    /// <summary>
    ///     Gets or sets the level of the actor.
    /// </summary>
    public int Level { get; internal set; }

    /// <summary>
    ///     Gets or sets the number of friends of the actor.
    /// </summary>
    public int Friends { get; internal set; }

    /// <summary>
    ///     Gets or sets the number of profile views of the actor.
    /// </summary>
    public int ProfileViews { get; internal set; }

    /// <summary>
    ///     Gets or sets the number of VIP friends of the actor.
    /// </summary>
    public int FriendsVip { get; internal set; }

    /// <summary>
    ///     Gets or sets the maximum number of friends of the actor.
    /// </summary>
    public int MaxFriends { get; internal set; }

    /// <summary>
    ///     Gets or sets the total number of VIP days of the actor.
    /// </summary>
    public int TotalVipDays { get; internal set; }

    /// <summary>
    ///     Gets or sets the fame of the actor.
    /// </summary>
    public long Fame { get; internal set; }

    /// <summary>
    ///     Gets or sets the number of star coins of the actor.
    /// </summary>
    public long StarCoins { get; internal set; }

    /// <summary>
    ///     Gets or sets the number of diamonds of the actor.
    /// </summary>
    public long Diamonds { get; internal set; }

    /// <summary>
    ///     Gets or sets the fortune of the actor.
    /// </summary>
    public long Fortune { get; internal set; }

    /// <summary>
    ///     Gets or sets a value indicating whether the actor is a moderator.
    /// </summary>
    public bool IsModerator { get; internal set; }

    /// <summary>
    ///     Gets or sets a value indicating whether the actor is a VIP.
    /// </summary>
    public bool IsVip { get; internal set; }

    /// <summary>
    ///     Gets or sets a value indicating whether the actor is deleted.
    /// </summary>
    public bool IsDeleted { get; internal set; }

    /// <summary>
    ///     Gets or sets a value indicating whether the actor is a jury.
    /// </summary>
    public bool IsJury { get; internal set; }

    /// <summary>
    ///     Gets or sets a value indicating whether the actor is a judge.
    /// </summary>
    public bool IsJudge { get; internal set; }

    /// <summary>
    ///     Gets or sets a value indicating whether the actor is a celebrity.
    /// </summary>
    public bool IsCeleb { get; internal set; }

    /// <summary>
    ///     Gets or sets a value indicating whether the actor is available.
    /// </summary>
    public bool IsAvailable { get; internal set; }

    /// <summary>
    ///     Gets the time since the actor was last active.
    /// </summary>
    public TimeSpan InActiveSince
        => DateTime.UtcNow - LastLoginAt;

    /// <summary>
    ///     Gets the timestamp of the actor's inactivity.
    /// </summary>
    public long InActiveTimeStamp
        => LastLoginAt.ToTimestamp();

    /// <summary>
    ///     Gets the list of rare items in the actor's inventory.
    /// </summary>
    public List<MspActorCloth> Rares
        => Inventory.Where(a => a.Cloth?.ShopId == -100).ToList();

    /// <summary>
    ///     Gets the fame until the next level.
    /// </summary>
    public long FameUntilNextLevel
    {
        get
        {
            var mspCurrency = MspClientUtil.GetLevels().FirstOrDefault(a => a.Level == Level + 1)?.Fame;
            if (mspCurrency != null)
                return mspCurrency.GetValueOrDefault() - Fame;

            return 0;
        }
    }

    /// <summary>
    ///     Gets the next level of the actor.
    /// </summary>
    public int NextLevel
        => MspClientUtil.GetLevels().FirstOrDefault(a => a.Level == Level + 1)?.Level ?? 0;

    /// <summary>
    ///     Gets the fame required for the next level.
    /// </summary>
    public long NextLevelFame
        => MspClientUtil.GetLevels().FirstOrDefault(a => a.Level == Level + 1)?.Fame ?? 0;

    /// <summary>
    ///     Gets the string representation of the calculated outfit.
    /// </summary>
    public string OutfitPriceDiscord
        => ClothRel.Count == 0 ? "<:sc:806006180837195777> 0\n<:dm:806006181244043264> 0"
               : ClothRel.Sum(a => a?.Cloth?.Price) != 0 && ClothRel.Sum(a => a?.Cloth?.DiamondsPrice) == 0
                   ? $"<:sc:806006180837195777> [**{ClothRel.Sum(a => a?.Cloth?.Price):N0}**](https://a)"
                   : ClothRel.Sum(a => a?.Cloth?.Price) == 0 && ClothRel.Sum(a => a?.Cloth?.DiamondsPrice) != 0
                       ? $"<:dm:806006181244043264> [**{ClothRel.Sum(a => a?.Cloth?.DiamondsPrice):N0}**](https://a)"
                       : ClothRel.Sum(a => a?.Cloth?.Price) != 0 && ClothRel.Sum(a => a?.Cloth?.DiamondsPrice) != 0
                           ? $"<:sc:806006180837195777> [**{ClothRel.Sum(a => a?.Cloth?.Price)}**](https://a)\n<:dm:806006181244043264> [**{ClothRel.Sum(a => a?.Cloth?.DiamondsPrice)}**](https://a)" : "<:sc:806006180837195777> [**0**](https://a)\n<:dm:806006181244043264> [**0**](https://a)";

    /// <summary>
    ///     Gets the diamond price of the outfit.
    /// </summary>
    public int? OutfitDiamondPrice
        => ClothRel.Count != 0 ? ClothRel.Sum(a => a?.Cloth?.DiamondsPrice) : 0;

    /// <summary>
    ///     Gets the star coin price of the outfit.
    /// </summary>
    public int? OutfitStarCoinPrice
        => ClothRel.Count != 0 ? ClothRel.Sum(a => a?.Cloth?.Price) : 0;

    /// <summary>
    ///     Gets the special friend at the specified index.
    /// </summary>
    /// <param name="index">The index of the special friend.</param>
    /// <returns>The special friend at the specified index.</returns>
    public MspSpecialFriend? GetSpecialFriend(int index)
        => Status.SpecialFriend?.ElementAtOrDefault(index);

    /// <summary>
    ///     Creates a new instance of the MspActor class with predefined values.
    /// </summary>
    /// <returns>A new instance of the MspActor class.</returns>
    public static MspActor CreateActor()
        => new()
        {
            Username = "Pixi Star",
            Server = Server.UnitedStates,
            Level = 75,
            Room = new(),
            IsJudge = true,
            IsAvailable = true,
            Fame = 139183732,
            Fortune = 9283273273,
            Gender = Gender.Female,
            LastLoginAt = DateTime.Now,
            MembershipPurchasedAt = DateTime.Now,
            TotalVipDays = 9999,
            Friends = 999,
            CreatedAt = new DateTime(2019, 10, 12),
            IsCeleb = true,
            IsVip = true,
            MaxFriends = 999999,
            StarCoins = 9929292,
            Diamonds = 120,
            FriendsVip = 283,
            IsDeleted = false,
            IsModerator = true,
            IsJury = true,
            ProfileViews = 999922,
            ProfileId = "US|3",
            Id = 3,
            VipTier = VipTierType.ELITE_VIP,
            ClothRel =
                [],
            Beauty = new(),
            Guestbooks =
                [],
            Pictures =
                [],
            Statuses =
                [],
            MembershipTimeoutAt = DateTime.Now,
            Status = new()
            {
                Likes = 92,
                Text = "I'm a test user",
                Color = StatusColor.Red
            },
            Avatar = new()
            {
                AvatarUrl = "https://snapshots.mspcdns.com/v1/MSP/fr/snapshot/movieStar/3.jpg",
                BodyUrl = "https://snapshots.mspcdns.com/v1/MSP/fr/snapshot/fullsizemoviestar/3.jpg"
            }
        };

    /// <summary>
    ///     Calculates the percentage of fame the actor has towards the next level.
    /// </summary>
    /// <returns>A string representing the percentage of fame towards the next level.</returns>
    public string GetPercentage()
    {
        var nextLevel = MspClientUtil.GetLevels()
            .FirstOrDefault(a => a.Level == Level + 1);

        if (nextLevel == null)
            return "100%";

        var percentage = (double)Fame / nextLevel.Fame * 100;
        return $"{percentage:N2}%";
    }

    /// <summary>
    ///     Generates a string containing the actor's clothing data in a specific format.
    /// </summary>
    /// <returns>A string representing the actor's clothing data.</returns>
    public string GetClothDiscordData()
    {
        StringBuilder builder = new();

        foreach (var cloth in ClothRel.Where(a => MspApi.GetCategoryType(a?.Cloth?.CategoryId) == CategoryType.Accessor))
            builder.AppendLine($"<:dolo_accessories:886692545256652891> **»** [Accessories](http://a) » **{cloth?.Cloth?.Name}**");

        foreach (var cloth in ClothRel.Where(a => MspApi.GetCategoryType(a?.Cloth?.CategoryId) == CategoryType.Head))
            builder.AppendLine($"<:dolo_headwear:886692545399242824> **»** [Headwear](http://a) » **{cloth?.Cloth?.Name}**");

        foreach (var cloth in ClothRel.Where(a => MspApi.GetCategoryType(a?.Cloth?.CategoryId) == CategoryType.Hair))
            builder.AppendLine($"<:dolo_hair:886692545239859221> **»** [Hair](http://a) » **{cloth?.Cloth?.Name}**");

        foreach (var cloth in ClothRel.Where(a => MspApi.GetCategoryType(a?.Cloth?.CategoryId) == CategoryType.Top))
            builder.AppendLine($"<:dolo_tops:886692545378263060> **»** [Tops](http://a) » **{cloth?.Cloth?.Name}**");

        foreach (var cloth in ClothRel.Where(a => MspApi.GetCategoryType(a?.Cloth?.CategoryId) == CategoryType.Bottom))
            builder.AppendLine($"<:dolo_bottom:886692545076277268> **»** [Bottoms](http://a) » **{cloth?.Cloth?.Name}**");

        foreach (var cloth in ClothRel.Where(a => MspApi.GetCategoryType(a?.Cloth?.CategoryId) == CategoryType.Foot))
            builder.AppendLine($"<:dolo_footwear:886692545147584522> **»** [Footwear](http://a) » **{cloth?.Cloth?.Name}**");



        return builder.ToString();
    }

    /// <summary>
    ///     Asynchronously retrieves the actor's summary.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains the actor's summary.</returns>
    public async Task<MspActorSummary> GetActorSummaryAsync()
        => MovieStarPlanet != null ? await MovieStarPlanet.GetActorSummaryAsync(Id) : new();


    /// <summary>
    ///     Asynchronously retrieves the actor's room.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains the actor's room.</returns>
    public async Task<MspRoom> GetRoomAsync()
        => MovieStarPlanet != null ? await MovieStarPlanet.GetActorRoomAsync(Id) : new();


    /// <summary>
    ///     Asynchronously retrieves the actor's status.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains the actor's status.</returns>
    public async Task<MspStatus> GetStatusAsync()
        => MovieStarPlanet != null ? await MovieStarPlanet.GetActorStatusAsync(Id) : new();


    /// <summary>
    ///     Asynchronously sends an autograph to the actor.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains the sent autograph.</returns>
    public async Task<MspAutograph> SendAutographAsync()
        => MovieStarPlanet != null ? await MovieStarPlanet.SendAutographAsync(Id) : new();

    /// <summary>
    ///     Asynchronously deletes the actor from the friend list.
    /// </summary>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result indicates whether the deletion was
    ///     successful.
    /// </returns>
    public async Task<MspResult<bool>> DeleteAsync()
        => MovieStarPlanet != null ? await MovieStarPlanet.DeleteFriendAsync(Id) : new();


    /// <summary>
    ///     Asynchronously retrieves the actor's boonsters.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains the actor's boonsters.</returns>
    public async Task<MspList<MspBoonster>> GetBoonstersAsync()
        => MovieStarPlanet != null ? await MovieStarPlanet.GetActorBoonstersAsync(Id) :
               [];


    /// <summary>
    ///     Asynchronously retrieves the actor's items.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains the actor's items.</returns>
    public async Task<MspList<MspActorCloth>> GetItemsAsync()
        => MovieStarPlanet != null ? await MovieStarPlanet.GetActorItemsAsync(Id) :
               [];


    /// <summary>
    ///     Retrieves the name of the actor's gender.
    /// </summary>
    /// <returns>The name of the actor's gender.</returns>
    public string? GetGenderName()
        => Enum.GetName(Gender);


    /// <summary>
    ///     Returns a string that represents the current object.
    /// </summary>
    /// <returns>A string that represents the current object.</returns>
    public override string ToString()
        => $"Account details for: {Username}\n" +
           $"    - Created {CreatedAt}\n" +
           $"    - StarCoins {StarCoins:N0}\n" +
           $"    - Fame {Fame:N0}\n" +
           $"    - FameNextLevel {FameUntilNextLevel:N0}\n" +
           $"    - FameForLevel {NextLevelFame:N0}\n" +
           $"    - FamePercentage {GetPercentage()}\n" +
           $"    - Level {Level}\n" +
           $"    - NextLevel {NextLevel}\n" +
           $"    - Diamonds {Diamonds:N0}\n" +
           $"    - VIP {IsVip}\n" +
           $"    - JUDGE {IsJudge}\n" +
           $"    - JURY {IsJury}\n" +
           $"    - CELEB {IsCeleb}\n";

    /// <summary>
    ///     Sets the Success property to false and returns the current instance.
    /// </summary>
    /// <returns>The current instance with the Success property set to false.</returns>
    public MspActor Unsuccessful()
    {
        Success = false;
        return this;
    }


    #region Internal Methods
    internal void SetBeauty(InternalActor mspActor, MspApi api)
    {
        Beauty = new()
        {
            MovieStarPlanet = api._client,
            Skincolor = mspActor.SkinColor,
            Eye = mspActor.Eye is null
                      ? default
                      : new MspEye
                      {
                          Id = mspActor.EyeId,
                          LastUpdatedAt = mspActor.Eye.LastUpdated,
                          Color = mspActor.EyeColors,
                          DefaultColors = mspActor.Eye.DefaultColors,
                          DiamondsPrice = mspActor.Eye.DiamondsPrice,
                          Discount = mspActor.Eye.Discount,
                          IsDragonBone = mspActor.Eye.DragonBone,
                          IsHidden = mspActor.Eye.Hidden,
                          SkinId = mspActor.Eye.SkinId,
                          IsNew = mspActor.Eye.IsNew != 0,
                          IsVip = mspActor.Eye.Vip != 0,
                          Price = mspActor.Eye.Price,
                          Sortorder = mspActor.Eye.Sortorder,
                          Swf = mspActor.Eye.Swf
                      },
            Mouth = mspActor.Mouth is null
                        ? default
                        : new MspMouth
                        {
                            Id = mspActor.MouthId,
                            LastUpdatedAt = mspActor.Mouth.LastUpdated,
                            DefaultColors = mspActor.Mouth.DefaultColors,
                            DiamondsPrice = mspActor.Mouth.DiamondsPrice,
                            Discount = mspActor.Mouth.Discount,
                            IsDragonBone = mspActor.Mouth.DragonBone,
                            IsHidden = mspActor.Mouth.Hidden,
                            SkinId = mspActor.Mouth.SkinId,
                            IsNew = mspActor.Mouth.IsNew != 0,
                            IsVip = mspActor.Mouth.Vip != 0,
                            Price = mspActor.Mouth.Price,
                            Sortorder = mspActor.Mouth.Sortorder,
                            Swf = mspActor.Mouth.Swf,
                            Color = mspActor.MouthColors != null
                                        ? mspActor.MouthColors.Contains(',')
                                              ? mspActor.MouthColors?.Split(',')[1]
                                              : mspActor.MouthColors
                                        : null
                        },
            Nose = mspActor.Nose is null
                       ? default
                       : new MspNose
                       {
                           Id = mspActor.NoseId,
                           LastUpdatedAt = mspActor.Nose.LastUpdated,
                           DefaultColors = mspActor.Nose.DefaultColors,
                           DiamondsPrice = mspActor.Nose.DiamondsPrice,
                           Discount = mspActor.Nose.Discount,
                           IsDragonBone = mspActor.Nose.DragonBone,
                           IsHidden = mspActor.Nose.Hidden,
                           SkinId = mspActor.Nose.SkinId,
                           IsNew = mspActor.Nose.IsNew != 0,
                           IsVip = mspActor.Nose.Vip != 0,
                           Price = mspActor.Nose.Price,
                           Sortorder = mspActor.Nose.Sortorder,
                           Swf = mspActor.Nose.Swf
                       },
            EyeShadow = mspActor.EyeShadow is null
                            ? null
                            : new MspEyeShadow
                            {
                                Id = mspActor.EyeShadowId,
                                LastUpdatedAt = mspActor.EyeShadow.LastUpdated,
                                DefaultColors = mspActor.EyeShadow.DefaultColors,
                                DiamondsPrice = mspActor.EyeShadow.DiamondsPrice,
                                Discount = mspActor.EyeShadow.Discount,
                                IsDragonBone = mspActor.EyeShadow.DragonBone,
                                IsHidden = mspActor.EyeShadow.Hidden,
                                SkinId = mspActor.EyeShadow.SkinId,
                                IsNew = mspActor.EyeShadow.IsNew != 0,
                                IsVip = mspActor.EyeShadow.Vip != 0,
                                Price = mspActor.EyeShadow.Price,
                                Sortorder = mspActor.EyeShadow.Sortorder,
                                Swf = mspActor.EyeShadow.Swf,
                                Color = mspActor.EyeShadowColors
                            }
        };
    }
    internal void SetClothRel(InternalActorClothesRel[]? actorClothesRel)
    {
        actorClothesRel?.ToList().ForEach(a =>
        {
            if (a.Cloth is null)
                return;

            ClothRel.Add(new()
            {
                ActorId = a.ActorId,
                ClothId = a.ClothesId,
                Color = a.Color,
                Id = a.ActorClothesRelId,
                IsWearing = a.IsWearing != 0,
                Cloth = new()
                {
                    CategoryId = a.Cloth.ClothesCategoryId,
                    ColorScheme = a.Cloth.ColorScheme,
                    DiamondsPrice = a.Cloth.DiamondsPrice,
                    Discount = a.Cloth.Discount,
                    Id = a.Cloth.ClothesCategoryId,
                    IsNew = a.Cloth.IsNew != 0,
                    IsVip = a.Cloth.Vip != 0,
                    LastUpdatedAt = a.Cloth.LastUpdated,
                    ThemeId = a.Cloth.ThemeId,
                    Name = a.Cloth.Name,
                    Price = a.Cloth.Price,
                    Scale = a.Cloth.Scale,
                    ShopId = a.Cloth.ShopId,
                    SkinId = a.Cloth.SkinId,
                    Sortorder = a.Cloth.Sortorder,
                    Filename = a.Cloth.Swf,
                    SwfUrl = a.Cloth?.Swf is null
                                 ? ""
                                 : MspApi.GetCategoryUrl(a.Cloth.ClothesCategoryId) + a.Cloth.Swf.Replace(" ", "%20") + ".swf",
                    Category = new()
                    {
                        Id = a.Cloth?.ClothesCategory?.ClothesCategoryId ?? 0,
                        Name = a.Cloth?.ClothesCategory?.Name,
                        SlotTypeId = a.Cloth?.ClothesCategory?.SlotTypeId ?? 0
                    }
                }
            });
        });
    }

    internal void SetSummary(MspActorSummary summary)
    {
        ProfileViews = summary.ProfileViews;
        CreatedAt = summary.Created;
    }

    internal void SetStatus (MspStatus status)
    {
        Status = status;
    }

    internal void SetDetails(InternalActor mspActor, MspApi api)
    {
        BaseId = mspActor.ActorId;
        TotalVipDays = mspActor.TotalVipDays;
        Server = api._client.User.Server;
        IsAvailable = true;
        Id = mspActor.ActorId;
        Username = mspActor.Name;
        ProfileId = mspActor.NebulaProfileId;
        Level = mspActor.Level;
        Friends = mspActor.FriendCount;
        FriendsVip = mspActor.FriendCountVip;
        MembershipPurchasedAt = mspActor.MembershipPurchasedDate;
        MembershipTimeoutAt = mspActor.MembershipTimeoutDate;
        LastLoginAt = mspActor.LastLogin;
        IsModerator = mspActor.Moderator != 0;
        IsVip = DateTime.UtcNow < mspActor.MembershipTimeoutDate;
        IsCeleb = mspActor.FriendCountVip >= 100;
        IsJudge = mspActor.TotalVipDays >= 180;
        IsJury = !(mspActor.TotalVipDays >= 180) && mspActor.TotalVipDays >= 90;
        IsDeleted = mspActor.IsExtra != 0;
        VipTier = !(DateTime.UtcNow < mspActor.MembershipTimeoutDate) || mspActor.VipTier == 0
                          ? VipTierType.NON_VIP
                          : (VipTierType)mspActor.VipTier;
        Gender = mspActor.SkinSwf == "femaleskin" ? Gender.Female : Gender.Male;
        StarCoins = mspActor.Money.To<long>();
        Diamonds = mspActor.Diamonds.To<long>();
        Fame = mspActor.Fame.To<long>();
        Fortune = mspActor.Fortune.To<long>();
        MaxFriends = MspApi.GetMaxFriends((int)VipTier, Level);
        RoomUrl = api.GetObfuscatedUrl(ObfuscationType.room, Id);
        BaseId = mspActor.ActorId;
        FriendStatus = Status.FriendStatus;

        if (Status.SpecialFriend != null)
            RelationShips.TryAddRange(Status.SpecialFriend);

        Avatar = new()
        {
            AvatarUrl = api.GetMovieStarAvatarUrl(Id),
            BodyUrl = api.GetMovieStarBodyUrl(Id)
        };
    }
    #endregion
}