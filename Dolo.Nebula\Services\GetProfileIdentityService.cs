using Dolo.Core.Http;
using Dolo.Nebula.Entities;

namespace Dolo.Nebula.Services;

internal static class GetProfileIdentityService
{
    public static async Task<object?> GetProfileIdentityAsync(NebulaClient client, string profileId)
    {
        if (string.IsNullOrEmpty(client.Config.Auth))
            throw new NullReferenceException("auth-key is required on this method");

        var data = await Http.TrySendAsync<object>(a => {
            a.Url = $"{client.Services?.GetProfileIdentity()}/profiles/{profileId}";
            a.Method = HttpMethod.Get;
            a.AuthToken = client.Config.Auth;
        });

        return data.Body;
    }
}
