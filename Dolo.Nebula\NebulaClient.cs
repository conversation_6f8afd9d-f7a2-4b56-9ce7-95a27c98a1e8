using Dolo.Core.Extension;
using Dolo.Core.Http;
using Dolo.Nebula.Entities;
using Dolo.Nebula.Enum;
using Dolo.Nebula.Services;
using Dolo.Planet;
using Dolo.Planet.Entities;
using System.Reflection;
using System.Runtime.CompilerServices;
[assembly: InternalsVisibleTo("Pluto")]
[assembly: InternalsVisibleTo("Dolo.Planet")]
[assembly: InternalsVisibleTo("Dolo.Test")]
[assembly: InternalsVisibleTo("Dolo.Pluto.Maui")]

namespace Dolo.Nebula;

[Obfuscation(Feature = "apply to member * when method: renaming", Exclude = true)]
[Obfuscation(Feature = "internalization", Exclude = true)]
public class NebulaClient
{

    /// <summary>
    ///     Configuration of NebulaClient
    /// </summary>
    internal readonly NebulaConfig Config;
    internal readonly MspClient? MspClient;
    internal readonly MspNebula? MspNebula;

    /// <summary>
    ///     All services in Nebula used for http request
    /// </summary>
    internal List<ServiceData>? Services;

    /// <summary>
    ///     Constructor for NebulaClient class
    /// </summary>
    /// <param name="config"></param>
    public NebulaClient(Action<NebulaConfig> config)
    {
        Config = config.GetAction();
        MspNebula = Config.Nebula;

        var authData = Config.GetAccessToken();
        if (!Config.UseAuthInformation || authData == null)
            return;

        Config.Server = authData.Name?.Split('|')[0].GetNebulaServerFromCode();
        Config.Game = authData.GameId?.GetNebulaGame();
    }

    /// <summary>
    ///     Check if the auth token is expired
    /// </summary>
    public bool IsExpired => MspNebula?.AccessToken?.IsJwtExpired() ?? Config.Auth?.IsJwtExpired() ?? false;
    public string? RefreshToken => MspNebula?.RefreshToken ?? Config.RefreshToken;
    /// <summary>
    ///     Create a new instance of NebulaClient
    /// </summary>
    /// <returns></returns>
    public static NebulaClient Create(Action<NebulaConfig> config) => new(config);

    /// <summary>
    ///     Try to initialize the NebulaClient when the method returns true
    ///     then the client is ready to use otherwise there was a problem
    /// </summary>
    /// <returns></returns>
    public async Task<bool> TryInitializeAsync()
    {
        if (IsExpired && MspClient != null)
        {
            var login = await LoginRefreshService.LoginRefreshAsync(this);
            MspClient.User
                .SetAccessToken(login?.Token)
                .SetRefreshToken(login?.RefreshToken);
        }

        var http = await Http.TrySendAsync<ServiceDataCollection>(a => {
            a.Url = $"https://disco.mspapis.com/disco/v1/services/msp2/{Config.Server?.GetNebulaRegion()}";
            a.Method = HttpMethod.Get;
            a.ContentType = HttpContentType.ApplicationJson;
        });

        if (!http.IsSuccess || http.Body is null)
            return false;

        Services = http.Body.Services;
        return true;
    }


    public async Task<string?> GetSuggestionNamesAsync(Server server) => await SuggestionNamesService.GetSuggestionNamesAsync(server);
    public static async Task<bool> TryGenerateAsync(Server server, Action<MspClient>? callback = null) => await GenerateService.GenerateAsync(server, callback);
    public static async Task<CreateProfileData> TryCreateProfileAsync(string? username, string? password, Server server) => await CreateProfileService.CreateAsync(username, password, server);
    public async Task<RelationshipData?> GetFriendsAsync(string? profileid = default) => await GetFriendsService.GetFriendsAsync(this, profileid);
    public async Task<List<ExperienceLevelData>?> GetLevelsAsync() => await GetLevelsService.GetLevelsAsync(this);
    public async Task<ProfileExperienceData?> GetProfileExperienceAsync(string profileid) => await GetProfileExperienceService.GetExperienceAsync(this, profileid);
    public async Task<GetProfilesResponse?> GetProfilesAsync(params string[] profiles) => await GetProfilesService.GetProfilesAsync(this, profiles);
    public async Task<object?> GetProfileAsync(string profile) => await GetProfileService.GetProfileAsync(this, profile);
    public async Task<object?> GetProfileIdentityAsync(string profileId) => await GetProfileIdentityService.GetProfileIdentityAsync(this, profileId);
    public async Task<NebulaResult<bool>> DeleteFriendAsync(string profile) => await DeleteRelationshipService.DeleteRelationshipAsync(this, profile);
    public async Task<bool> RequestFriendAsync(string profile) => await RequestRelationShipService.RequestRelationshipAsync(this, profile);
    public async Task<List<ProfileData>?> SearchProfileAsync(string profile, int pageSize, int currentPage = 0) => await GetProfileSearchService.SearchProfileAsync(this, profile, pageSize, currentPage);
    public async Task PurchaseItemAsync(IEnumerable<ItemsPurchaseData> items) => await PurchaseItemService.PurchaseItemAsync(this, items);
    public async Task<LoginRefreshResponse?> RefreshLoginAsync() => await LoginRefreshService.LoginRefreshAsync(this);
    public async Task<bool> GetLockedStatusAsync(string? profileId) => await LockedService.GetLockedStatusAsync(this, profileId);
}
